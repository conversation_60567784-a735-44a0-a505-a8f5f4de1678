#pragma once

#include "esphome.h"
#include <math.h>
#include <algorithm>

using namespace esphome;

struct TagInfoBean {
    double x;           // X坐标，单位mm
    double y;           // Y坐标，单位mm
    double speed;       // 速度，单位cm/s
    double resolution;  // 距离分辨率，单位mm
    double distance;    // 计算得出的距离，单位m
    double angle;       // 计算得出的角度，单位度
    unsigned long timestamp;
};

class LD2450Component : public Component, public UARTDevice {
public:
    LD2450Component() : UARTDevice() {}

    void setup() override {
        ESP_LOGD("LD2450", "Setting up LD2450 component");
        this->detection_enabled_ = true;
        this->last_detection_time_ = 0;
        this->last_heartbeat_ = 0;
        this->current_target_count_ = 0;
        
        // Initialize targets
        for (int i = 0; i < 3; i++) {
            this->current_targets_[i] = {};
        }
    }

    void loop() override {
        // Heartbeat every 10 seconds
        if (millis() - this->last_heartbeat_ > 10000) {
            ESP_LOGD("LD2450", "Heartbeat - Detection: %s", this->detection_enabled_ ? "ON" : "OFF");
            this->last_heartbeat_ = millis();
        }

        // Only process data when detection is enabled
        if (this->detection_enabled_ && this->available()) {
            ESP_LOGD("LD2450", "Data detected!");
            
            std::string serial_data = this->read_serial_data();
            
            if (serial_data.length() < 60) { // LD2450完整帧需要60个十六进制字符（30字节）
                ESP_LOGW("LD2450", "Data too short: %d (need minimum 60)", serial_data.length());
                return;
            }
            
            TagInfoBean results[3];
            int count = 0;
            
            this->analysis_area_coordinate(serial_data, results, count);
            ESP_LOGD("LD2450", "Parsing completed, found targets: %d", count);
            
            if (count > 0) {
                // Update current target data
                this->current_target_count_ = count;
                for (int i = 0; i < count; i++) {
                    this->current_targets_[i] = results[i];
                }
                this->last_detection_time_ = millis();
                
                // Update ESPHome sensors
                this->update_sensors();
                
                ESP_LOGI("LD2450", "Found %d targets", count);
                for (int i = 0; i < count; i++) {
                    ESP_LOGD("LD2450", "Target %d - X:%.1fmm Y:%.1fmm Speed:%.1fcm/s Res:%.1fmm", 
                             i + 1, results[i].x, results[i].y, results[i].speed, results[i].resolution);
                }
            } else {
                ESP_LOGD("LD2450", "No valid targets detected in this frame");
                this->current_target_count_ = 0;
                this->update_sensors(); // Clear sensors
            }
        }
        // Clear buffer if detection is disabled
        else if (!this->detection_enabled_ && this->available()) {
            int cleared = 0;
            while (this->available() && cleared < 50) {
                this->read();
                cleared++;
            }
        }
    }

    void set_detection_enabled(bool enabled) {
        this->detection_enabled_ = enabled;
        ESP_LOGI("LD2450", "Detection %s", enabled ? "enabled" : "disabled");
    }

    bool get_detection_enabled() const {
        return this->detection_enabled_;
    }

    int get_target_count() const {
        return this->current_target_count_;
    }

    const TagInfoBean* get_targets() const {
        return this->current_targets_;
    }

private:
    bool detection_enabled_;
    unsigned long last_detection_time_;
    unsigned long last_heartbeat_;
    TagInfoBean current_targets_[3];
    int current_target_count_;
    
    static const std::string NOT_TARGET_DATA_STR;
    static const int SINGLE_TARGET_DATA_LENGTH = 16;
    static const int TOTAL_FRAME_LENGTH = 60;

    int hex_to_int(const std::string& hex) {
        return (int)strtol(hex.c_str(), NULL, 16);
    }

    // 根据LD2450协议解析X坐标
    double calculate_x_value(const std::string& str) {
        int byte_low = hex_to_int(str.substr(0, 2));
        int byte_high = hex_to_int(str.substr(2, 2));
        int raw_value = byte_low + byte_high * 256; // 小端字节序

        // 按协议：高位字节最高位为1表示正，0表示负
        if (byte_high >= 128) {
            // 正坐标：修正范围，去掉符号偏移（减去32768）
            return (double)(raw_value - 32768);
        } else {
            // 负坐标：直接是负值
            return (double)(-raw_value);
        }
    }

    // 根据LD2450协议解析Y坐标  
    double calculate_y_value(const std::string& str) {
        int byte_low = hex_to_int(str.substr(4, 2));
        int byte_high = hex_to_int(str.substr(6, 2));
        int value = byte_low + byte_high * 256; // 小端字节序
        
        // signed int16处理：最高位1=正坐标，0=负坐标
        if (byte_high >= 128) {
            // 正坐标：value - 2^15 (因为示例显示34481 - 32768 = 1713)
            return (double)(value - 32768);
        } else {
            // 负坐标：0 - value
            return (double)(0 - value);
        }
    }

    // 根据LD2450协议解析速度
    double calculate_speed(const std::string& str) {
        int byte_low = hex_to_int(str.substr(8, 2));
        int byte_high = hex_to_int(str.substr(10, 2));
        int value = byte_low + byte_high * 256; // 小端字节序
        
        // signed int16处理：最高位1=正向速度，0=负向速度
        if (byte_high >= 128) {
            // 正向速度：取低15位
            return (double)(value & 0x7FFF); // 保留低15位
        } else {
            // 负向速度：取相反数
            return (double)(-value);
        }
    }

    // 根据LD2450协议解析距离分辨率
    double calculate_resolution(const std::string& str) {
        int byte_low = hex_to_int(str.substr(12, 2));
        int byte_high = hex_to_int(str.substr(14, 2));
        int value = byte_low + byte_high * 256; // 小端字节序
        
        // uint16类型，直接返回
        return (double)value;
    }

    double calculate_distance(double x, double y) {
        return sqrt(x * x + y * y);
    }

    double calculate_angle(double x, double y) {
        double angle = atan2(y, x) * (180.0 / M_PI);
        return -(90.0 - angle);
    }

    // 查找有效的数据帧起始位置
    int find_frame_start(const std::string& data) {
        // LD2450的帧头: AA FF 03 00 (8个十六进制字符)
        std::string frame_header = "aaff0300";
        
        for (size_t i = 0; i <= data.length() - 8; i += 2) { // 每2个字符（1个字节）检查一次
            std::string possible_header = data.substr(i, 8);
            std::transform(possible_header.begin(), possible_header.end(), possible_header.begin(), ::tolower);
            if (possible_header == frame_header) {
                ESP_LOGD("LD2450", "Found frame header at position: %d", i);
                return i;
            }
        }
        
        ESP_LOGD("LD2450", "No valid frame header found");
        return -1; // 没有找到有效帧头
    }

    // 验证帧尾
    bool validate_frame_tail(const std::string& data, int frame_start) {
        // LD2450的帧尾: 55 CC (4个十六进制字符)
        std::string frame_tail = "55cc";
        
        // 计算帧尾位置：帧头(8) + 目标数据(48) = 56个字符后
        int tail_position = frame_start + 56;
        
        if (tail_position + 4 > data.length()) {
            ESP_LOGD("LD2450", "Data too short for complete frame with tail");
            return false;
        }
        
        std::string actual_tail = data.substr(tail_position, 4);
        std::transform(actual_tail.begin(), actual_tail.end(), actual_tail.begin(), ::tolower);
        bool is_valid = actual_tail == frame_tail;
        
        ESP_LOGD("LD2450", "Frame tail at position %d: %s (expected: %s) - %s", 
                 tail_position, actual_tail.c_str(), frame_tail.c_str(), is_valid ? "VALID" : "INVALID");
        
        return is_valid;
    }

    void analysis_area_coordinate(const std::string& str_data, TagInfoBean results[], int& result_count) {
        result_count = 0;
        
        // 由于read_serial_data已经确保返回完整的30字节帧数据（从AAFF0300开始）
        // 不需要再次查找帧头，直接从第8个字符开始解析目标数据
        if (str_data.length() < TOTAL_FRAME_LENGTH) {
            return; // 数据不完整，直接返回
        }
        
        // 从帧头后开始解析目标数据（跳过4字节帧头 = 8个十六进制字符）
        int data_start_index = 8;
        
        for (int i = 0; i < 3; i++) {
            int segment_start = data_start_index + (i * SINGLE_TARGET_DATA_LENGTH);
            std::string segment = str_data.substr(segment_start, SINGLE_TARGET_DATA_LENGTH);

            if (segment != NOT_TARGET_DATA_STR && segment.length() == SINGLE_TARGET_DATA_LENGTH) {
                double x = calculate_x_value(segment);
                double y = calculate_y_value(segment);
                double speed = calculate_speed(segment);
                double resolution = calculate_resolution(segment);
                double distance = calculate_distance(x, y) / 1000.0; // 单位mm转为m
                double angle = calculate_angle(x, y);

                results[result_count].x = x;
                results[result_count].y = y;
                results[result_count].speed = speed;
                results[result_count].resolution = resolution;
                results[result_count].distance = distance;
                results[result_count].angle = angle;
                results[result_count].timestamp = millis();
                result_count++;
                
                ESP_LOGD("LD2450", "Valid target found - X: %.1fmm, Y: %.1fmm, Speed: %.1fcm/s, Resolution: %.1fmm", 
                         x, y, speed, resolution);
            } else {
                ESP_LOGD("LD2450", "No target at position %d", i + 1);
            }
        }
    }

    std::string read_serial_data() {
        // 帧头: AA FF 03 00
        uint8_t frame_header[4] = {0xAA, 0xFF, 0x03, 0x00};
        uint8_t buffer[4] = {0}; // 用于检测帧头的循环缓冲区
        int buffer_index = 0;
        bool frame_found = false;
        unsigned long start_time = millis();
        int bytes_checked = 0;

        // 第一阶段：寻找帧头
        while ((millis() - start_time < 100) && !frame_found && this->available()) {
            uint8_t c = this->read();
            buffer[buffer_index] = c;
            bytes_checked++;

            // 只有读取了至少4个字节后才开始检查帧头
            if (bytes_checked >= 4) {
                // 检查是否找到帧头 - 修正索引计算
                bool header_match = true;
                for (int i = 0; i < 4; i++) {
                    int check_index = (buffer_index + 1 + i) % 4; // 从下一个位置开始按顺序检查
                    if (buffer[check_index] != frame_header[i]) {
                        header_match = false;
                        break;
                    }
                }
                if (header_match) {
                    frame_found = true;
                    ESP_LOGV("LD2450", "Frame header found!");
                }
            }

            buffer_index = (buffer_index + 1) % 4;
        }

        if (!frame_found) {
            ESP_LOGV("LD2450", "No frame header found");
            return "";
        }

        // 第二阶段：从帧头开始读取完整帧(30字节)
        char hex_buffer[61]; // 30字节 = 60字符 + 1结尾
        int index = 0;

        // 先写入已找到的帧头
        sprintf(&hex_buffer[index], "AAFF0300");
        index += 8;

        // 继续读取剩余的26字节
        start_time = millis();
        int remaining_bytes = 26;

        while ((millis() - start_time < 50) && remaining_bytes > 0 && this->available()) {
            uint8_t c = this->read();
            sprintf(&hex_buffer[index], "%02X", c);
            index += 2;
            remaining_bytes--;
        }

        hex_buffer[index] = '\0';
        std::string data = std::string(hex_buffer);
        if (data.length() > 0) {
            ESP_LOGV("LD2450", "Frame: %d bytes", index / 2);
        }

        return data;
    }

    void update_sensors() {
        // Update target count
        id(target_count).publish_state(this->current_target_count_);

        // Update detection active status
        id(detection_active).publish_state(this->detection_enabled_);

        // Update target sensors
        for (int i = 0; i < 3; i++) {
            bool has_target = i < this->current_target_count_;

            // Update binary sensors for target detection
            switch (i) {
                case 0:
                    id(target1_detected).publish_state(has_target);
                    if (has_target) {
                        id(target1_x).publish_state(this->current_targets_[i].x);
                        id(target1_y).publish_state(this->current_targets_[i].y);
                        id(target1_speed).publish_state(this->current_targets_[i].speed);
                        id(target1_distance).publish_state(this->current_targets_[i].distance);
                        id(target1_angle).publish_state(this->current_targets_[i].angle);
                    } else {
                        id(target1_x).publish_state(NAN);
                        id(target1_y).publish_state(NAN);
                        id(target1_speed).publish_state(NAN);
                        id(target1_distance).publish_state(NAN);
                        id(target1_angle).publish_state(NAN);
                    }
                    break;
                case 1:
                    id(target2_detected).publish_state(has_target);
                    if (has_target) {
                        id(target2_x).publish_state(this->current_targets_[i].x);
                        id(target2_y).publish_state(this->current_targets_[i].y);
                        id(target2_speed).publish_state(this->current_targets_[i].speed);
                        id(target2_distance).publish_state(this->current_targets_[i].distance);
                        id(target2_angle).publish_state(this->current_targets_[i].angle);
                    } else {
                        id(target2_x).publish_state(NAN);
                        id(target2_y).publish_state(NAN);
                        id(target2_speed).publish_state(NAN);
                        id(target2_distance).publish_state(NAN);
                        id(target2_angle).publish_state(NAN);
                    }
                    break;
                case 2:
                    id(target3_detected).publish_state(has_target);
                    if (has_target) {
                        id(target3_x).publish_state(this->current_targets_[i].x);
                        id(target3_y).publish_state(this->current_targets_[i].y);
                        id(target3_speed).publish_state(this->current_targets_[i].speed);
                        id(target3_distance).publish_state(this->current_targets_[i].distance);
                        id(target3_angle).publish_state(this->current_targets_[i].angle);
                    } else {
                        id(target3_x).publish_state(NAN);
                        id(target3_y).publish_state(NAN);
                        id(target3_speed).publish_state(NAN);
                        id(target3_distance).publish_state(NAN);
                        id(target3_angle).publish_state(NAN);
                    }
                    break;
            }
        }
    }
};

// Static member definition
const std::string LD2450Component::NOT_TARGET_DATA_STR = "0000000000000000";

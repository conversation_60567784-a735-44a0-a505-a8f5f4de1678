{"storage_version": 1, "name": "bmp_aht_esp32c3", "friendly_name": "红外遥控, 温湿度大气压强传感器节点", "comment": null, "esphome_version": "2025.7.2", "src_version": 1, "address": "bmp_aht_esp32c3.local", "web_port": null, "esp_platform": "ESP32C3", "build_path": "C:\\git-program\\Embedded\\MY-ESPHOME\\.esphome\\build\\bmp_aht_esp32c3", "firmware_bin_path": "C:\\git-program\\Embedded\\MY-ESPHOME\\.esphome\\build\\bmp_aht_esp32c3\\.pioenvs\\bmp_aht_esp32c3\\firmware.bin", "loaded_integrations": ["aht10", "api", "binary_sensor", "bmp280_base", "bmp280_i2c", "button", "esp32", "esphome", "i2c", "logger", "md5", "mdns", "network", "ota", "preferences", "remote_base", "remote_receiver", "remote_transmitter", "safe_mode", "sensor", "socket", "switch", "template", "wifi"], "loaded_platforms": ["button/template", "ota/esphome", "sensor/aht10", "sensor/bmp280_i2c", "switch/template"], "no_mdns": false, "framework": "a<PERSON><PERSON><PERSON>", "core_platform": "esp32"}
esphome:
  name: radar-esp32c3
  friendly_name: "LD2450 Radar Sensor"
  platformio_options:
    board_build.flash_mode: dio

esp32:
  board: esp32-c3-devkitm-1
  framework:
    type: arduino

# Enable logging
logger:
  level: INFO
  baud_rate: 115200

# Enable Home Assistant API
api:

# Enable OTA updates
ota:
  - platform: esphome
    password: !secret ota_password

# WiFi configuration
wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password

  # Enable fallback hotspot (captive portal) in case wifi connection fails
  ap:
    ssid: "Radar-ESP32C3 Fallback Hotspot"
    password: "configesp32"

captive_portal:

# Web server for debugging
web_server:
  port: 80
  version: 2

# UART configuration for LD2450
uart:
  id: ld2450_uart
  tx_pin: GPIO0
  rx_pin: GPIO1
  baud_rate: 256000
  data_bits: 8
  parity: NONE
  stop_bits: 1

# Global variables for LD2450 data
globals:
  - id: detection_enabled
    type: bool
    restore_value: true
    initial_value: 'true'
  - id: target_count
    type: int
    restore_value: false
    initial_value: '0'
  - id: target1_x
    type: float
    restore_value: false
    initial_value: '0.0'
  - id: target1_y
    type: float
    restore_value: false
    initial_value: '0.0'
  - id: target1_speed
    type: float
    restore_value: false
    initial_value: '0.0'
  - id: target1_distance
    type: float
    restore_value: false
    initial_value: '0.0'
  - id: target1_angle
    type: float
    restore_value: false
    initial_value: '0.0'

# Interval component to read UART data
interval:
  - interval: 50ms
    then:
      - lambda: |-
          if (!id(detection_enabled)) return;

          // Check if data is available
          if (id(ld2450_uart).available()) {
            ESP_LOGD("LD2450", "Data available");

            // Read available bytes
            std::vector<uint8_t> data;
            while (id(ld2450_uart).available()) {
              data.push_back(id(ld2450_uart).read());
              if (data.size() > 100) break; // Prevent overflow
            }

            if (data.size() < 30) return; // Need at least 30 bytes for complete frame

            // Look for frame header AA FF 03 00
            int frame_start = -1;
            for (int i = 0; i <= (int)data.size() - 30; i++) {
              if (data[i] == 0xAA && data[i+1] == 0xFF &&
                  data[i+2] == 0x03 && data[i+3] == 0x00) {
                frame_start = i;
                break;
              }
            }

            if (frame_start == -1 || frame_start + 30 > data.size()) return;

            // Parse first target (8 bytes starting at offset 4)
            int target_offset = frame_start + 4;

            // Check if target data is not all zeros
            bool has_target = false;
            for (int i = 0; i < 8; i++) {
              if (data[target_offset + i] != 0) {
                has_target = true;
                break;
              }
            }

            if (has_target) {
              // Parse X coordinate (bytes 0-1, little endian, signed)
              int16_t x_raw = data[target_offset] | (data[target_offset + 1] << 8);
              float x = (float)x_raw;

              // Parse Y coordinate (bytes 2-3, little endian, signed)
              int16_t y_raw = data[target_offset + 2] | (data[target_offset + 3] << 8);
              float y = (float)y_raw;

              // Parse speed (bytes 4-5, little endian, signed)
              int16_t speed_raw = data[target_offset + 4] | (data[target_offset + 5] << 8);
              float speed = (float)speed_raw;

              // Calculate distance and angle
              float distance = sqrt(x*x + y*y) / 1000.0; // mm to m
              float angle = atan2(y, x) * 180.0 / 3.14159 - 90.0;

              // Update global variables
              id(target_count) = 1;
              id(target1_x) = x;
              id(target1_y) = y;
              id(target1_speed) = speed;
              id(target1_distance) = distance;
              id(target1_angle) = angle;

              ESP_LOGD("LD2450", "Target: X=%.1f Y=%.1f Speed=%.1f Dist=%.3f Angle=%.1f",
                       x, y, speed, distance, angle);
            } else {
              id(target_count) = 0;
            }
          }

# Sensors for radar data
sensor:
  # Target 1 sensors
  - platform: template
    name: "Target 1 X Position"
    id: target1_x_sensor
    unit_of_measurement: "mm"
    accuracy_decimals: 1
    state_class: "measurement"
    update_interval: 100ms
    lambda: |-
      return id(target_count) > 0 ? id(target1_x) : NAN;

  - platform: template
    name: "Target 1 Y Position"
    id: target1_y_sensor
    unit_of_measurement: "mm"
    accuracy_decimals: 1
    state_class: "measurement"
    update_interval: 100ms
    lambda: |-
      return id(target_count) > 0 ? id(target1_y) : NAN;

  - platform: template
    name: "Target 1 Speed"
    id: target1_speed_sensor
    unit_of_measurement: "cm/s"
    accuracy_decimals: 1
    state_class: "measurement"
    update_interval: 100ms
    lambda: |-
      return id(target_count) > 0 ? id(target1_speed) : NAN;

  - platform: template
    name: "Target 1 Distance"
    id: target1_distance_sensor
    unit_of_measurement: "m"
    accuracy_decimals: 3
    state_class: "measurement"
    update_interval: 100ms
    lambda: |-
      return id(target_count) > 0 ? id(target1_distance) : NAN;

  - platform: template
    name: "Target 1 Angle"
    id: target1_angle_sensor
    unit_of_measurement: "°"
    accuracy_decimals: 1
    state_class: "measurement"
    update_interval: 100ms
    lambda: |-
      return id(target_count) > 0 ? id(target1_angle) : NAN;

  # System sensors
  - platform: template
    name: "Target Count"
    id: target_count_sensor
    accuracy_decimals: 0
    state_class: "measurement"
    update_interval: 100ms
    lambda: |-
      return id(target_count);

# Binary sensors
binary_sensor:
  - platform: template
    name: "Detection Active"
    id: detection_active_sensor
    lambda: |-
      return id(detection_enabled);

  - platform: template
    name: "Target 1 Detected"
    id: target1_detected_sensor
    lambda: |-
      return id(target_count) > 0;

# Switches
switch:
  - platform: template
    name: "Enable Detection"
    id: enable_detection_switch
    optimistic: true
    restore_mode: RESTORE_DEFAULT_ON
    turn_on_action:
      - lambda: |-
          id(detection_enabled) = true;
    turn_off_action:
      - lambda: |-
          id(detection_enabled) = false;

# Text sensors for additional information
text_sensor:
  - platform: template
    name: "Radar Status"
    id: radar_status
    update_interval: 5s
    lambda: |-
      if (id(detection_enabled)) {
        int count = id(target_count);
        if (count > 0) {
          return {"Detecting " + to_string(count) + " target(s)"};
        } else {
          return {"Active - No targets"};
        }
      } else {
        return {"Detection disabled"};
      }

# LD2450 component implementation is now inline above
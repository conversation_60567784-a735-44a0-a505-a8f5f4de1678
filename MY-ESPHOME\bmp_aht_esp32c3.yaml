esphome:
  name: bmp_aht_esp32c3
  friendly_name: 红外遥控, 温湿度大气压强传感器节点

esp32:
  board: airm2m_core_esp32c3
  framework:
    type: arduino
logger:

api:

ota:
  platform: esphome

wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password

remote_transmitter:
  pin: GPIO19
  carrier_duty_percent: 50%
  id: my_transmitter

# remote_receiver:
#   pin: GPIO18
#   dump: all
#   id: my_receiver

i2c:
  sda: 4
  scl: 5
  scan: true
  id: bus_a

sensor:
  # AHT20 Temperature and Humidity Sensor (Fixed address 0x38)
  - platform: aht10
    temperature:
      name: "AHT20 Temperature"
      id: room_temperature
    humidity:
      name: "AHT20 Humidity"
    update_interval: 60s

  # BMP280 Pressure Sensor
  - platform: bmp280_i2c
    temperature:
      name: "BMP280 Temperature"
    pressure:
      name: "BMP280 Pressure"
    address: 0x77
    i2c_id: bus_a
    update_interval: 60s

# climate:
#   - platform: gree
#     name: Gree Air Conditioner
#     model: yac1fb9
#     transmitter_id: my_transmitter
#     sensor: room_temperature
#     min_temperature: 16 °C
#     max_temperature: 30 °C

switch:
  # TECO协议空调电源开关
  - platform: template
    name: "TECO AC Power"
    id: teco_ac_power
    optimistic: true
    turn_on_action:
      - remote_transmitter.transmit_raw:
          transmitter_id: my_transmitter
          carrier_frequency: 38kHz
          code: [ 8996, -4482, 652, -1684, 624, -558, 648, -556, 650, -1684, 624, -1684, 622, -1684, 624, -554, 650, -556, 650, -1684, 624, -1684, 622, -556, 648, -1684, 624, -556, 650, -558, 648, -558, 650, -556, 648, -556, 650, -556, 650, -558, 648, -556, 648, -556, 650, -1684, 622, -558, 646, -554, 652, -554, 650, -560, 646, -582, 624, -556, 650, -1684, 624, -556, 650, -1682, 624, -554, 652, -554, 652, -1682, 624, -554, 650]
      - delay: 80ms
      - remote_transmitter.transmit_raw:
          transmitter_id: my_transmitter
          carrier_frequency: 38kHz
          code: [4526, -602, 1688, -604, 586, -602, 586, -602, 1686, -578, 610, -604, 586, -602, 1688, -604, 584, -602, 586, -606, 1684, -578, 610, -604, 1686, -602, 586, -604, 584, -602, 588, -578, 610, -578, 610, -602, 586, -604, 586, -598, 592, -578, 610, -602, 1686, -602, 586, -604, 588, -602, 588, -602, 586, -604, 586, -604, 586, -578, 1710, -604, 1686, -602, 1688, -602, 586, -600, 590, -602, 1688, -602, 586, -602]
    turn_off_action:
      - remote_transmitter.transmit_raw:
          transmitter_id: my_transmitter
          carrier_frequency: 38kHz
          code: [8980, -4518, 606, -1682, 614, -576, 582, -606, 606, -584, 582, -606, 612, -576, 582, -1708, 580, -608, 582, -610, 606, -1682, 582, -608, 580, -1706, 582, -610, 578, -610, 580, -608, 580, -608, 582, -608, 582, -606, 610, -580, 608, -580, 580, -610, 580, -1708, 606, -582, 582, -608, 606, -582, 606, -584, 608, -580, 608, -582, 578, -1710, 582, -610, 578, -1710, 604, -584, 580, -608, 580, -1708, 608, -582, 582]
      - delay: 80ms
      - remote_transmitter.transmit_raw:
            transmitter_id: my_transmitter
            carrier_frequency: 38kHz
            code: [8990, -4530, 554, -1736, 578, -590, 598, -612, 578, -610, 576, -612, 576, -614, 576, -1712, 576, -612, 578, -610, 578, -1712, 578, -612, 578, -1710, 576, -612, 576, -612, 576, -612, 578, -612, 578, -612, 576, -612, 578, -612, 576, -612, 576, -614, 576, -1712, 576, -614, 576, -612, 576, -614, 576, -612, 576, -612, 576, -614, 578, -1712, 576, -1714, 576, -1714, 574, -614, 576, -614, 576, -1714, 576, -614, 576]

button:
  # TECO协议空调升温按钮
  - platform: template
    name: "TECO AC Temperature Up"
    on_press:
      - remote_transmitter.transmit_raw:
          transmitter_id: my_transmitter
          carrier_frequency: 38kHz
          code: [9022, -4484, 650, -1682, 624, -556, 650, -556, 652, -1682, 624, -1684, 622, -1684, 624, -554, 652, -558, 646, -1682, 624, -1682, 626, -582, 624, -1682, 626, -554, 652, -556, 650, -554, 652, -556, 648, -556, 650, -584, 624, -556, 648, -554, 652, -556, 650, -1682, 624, -558, 650, -554, 650, -556, 652, -556, 650, -582, 624, -556, 648, -1682, 624, -582, 624, -1682, 626, -554, 650, -556, 650, -1682, 624, -556, 650]

  # TECO协议空调降温按钮
  - platform: template
    name: "TECO AC Temperature Down"
    on_press:
      - remote_transmitter.transmit_raw:
          transmitter_id: my_transmitter
          carrier_frequency: 38kHz
          code: [8988, -4488, 658, -1680, 624, -558, 650, -554, 676, -1656, 626, -1682, 626, -1684, 624, -584, 624, -554, 650, -554, 652, -1682, 624, -558, 650, -1684, 624, -582, 622, -558, 648, -554, 650, -558, 648, -554, 650, -558, 648, -558, 648, -554, 650, -556, 650, -1684, 624, -558, 648, -558, 648, -584, 620, -558, 648, -556, 650, -558, 648, -1684, 624, -558, 648, -1688, 616, -560, 648, -558, 646, -1686, 622, -558, 648]

esphome:
  name: bmp_aht_esp32c3
  friendly_name: 红外遥控, 温湿度大气压强传感器节点

esp32:
  board: airm2m_core_esp32c3
  framework:
    type: arduino
logger:

api:

ota:
  platform: esphome

wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password

remote_transmitter:
  pin: GPIO19
  carrier_duty_percent: 50%
  id: my_transmitter

remote_receiver:
  pin: GPIO18
  dump: all
  id: my_receiver

i2c:
  sda: 4
  scl: 5
  scan: true
  id: bus_a

sensor:
  # AHT20 Temperature and Humidity Sensor (Fixed address 0x38)
  - platform: aht10
    temperature:
      name: "AHT20 Temperature"
      id: room_temperature
    humidity:
      name: "AHT20 Humidity"
    update_interval: 60s

  # BMP280 Pressure Sensor
  - platform: bmp280_i2c
    temperature:
      name: "BMP280 Temperature"
    pressure:
      name: "BMP280 Pressure"
    address: 0x77
    i2c_id: bus_a
    update_interval: 60s

# climate:
#   - platform: gree
#     name: Gree Air Conditioner
#     model: yac1fb9
#     transmitter_id: my_transmitter
#     sensor: room_temperature
#     min_temperature: 16 °C
#     max_temperature: 30 °C

switch:
  # TECO协议空调电源开关
  - platform: template
    name: "TECO AC Power"
    id: teco_ac_power
    optimistic: true
    turn_on_action:
      - remote_transmitter.transmit_raw:
          transmitter_id: my_transmitter
          carrier_frequency: 38kHz
          code: [8984, -4518, 624, -1680, 626, -582, 624, -582, 622, -1686, 648, -1658, 622, -1686, 622, -584, 622, -582, 620, -1688, 622, -1684, 622, -584, 624, -1686, 622, -584, 620, -584, 620, -588, 620, -582, 624, -580, 624, -608, 598, -608, 598, -584, 624, -584, 618, -1688, 620, -586, 620, -608, 598, -584, 646, -564, 644, -558, 618, -590, 618, -1710, 596, -610, 598, -1688, 618, -608, 602, -604, 596, -1710, 598, -608, 596]
    turn_off_action:
      - remote_transmitter.transmit_raw:
          transmitter_id: my_transmitter
          carrier_frequency: 38kHz
          code: [8984, -4490, 650, -1682, 624, -552, 654, -582, 622, -556, 650, -1682, 624, -1684, 622, -554, 652, -556, 650, -1684, 624, -1684, 624, -554, 652, -1682, 624, -558, 648, -558, 648, -556, 650, -556, 650, -554, 652, -582, 622, -556, 650, -558, 648, -582, 622, -1682, 624, -554, 652, -582, 622, -558, 648, -556, 648, -558, 648, -554, 652, -1682, 624, -556, 648, -1682, 624, -558, 650, -554, 652, -1682, 624, -552, 656]

button:
  # TECO协议空调升温按钮
  - platform: template
    name: "TECO AC Temperature Up"
    on_press:
      - remote_transmitter.transmit_raw:
          transmitter_id: my_transmitter
          carrier_frequency: 38kHz
          code: [9022, -4484, 650, -1682, 624, -556, 650, -556, 652, -1682, 624, -1684, 622, -1684, 624, -554, 652, -558, 646, -1682, 624, -1682, 626, -582, 624, -1682, 626, -554, 652, -556, 650, -554, 652, -556, 648, -556, 650, -584, 624, -556, 648, -554, 652, -556, 650, -1682, 624, -558, 650, -554, 650, -556, 652, -556, 650, -582, 624, -556, 648, -1682, 624, -582, 624, -1682, 626, -554, 650, -556, 650, -1682, 624, -556, 650]

  # TECO协议空调降温按钮
  - platform: template
    name: "TECO AC Temperature Down"
    on_press:
      - remote_transmitter.transmit_raw:
          transmitter_id: my_transmitter
          carrier_frequency: 38kHz
          code: [8988, -4488, 658, -1680, 624, -558, 650, -554, 676, -1656, 626, -1682, 626, -1684, 624, -584, 624, -554, 650, -554, 652, -1682, 624, -558, 650, -1684, 624, -582, 622, -558, 648, -554, 650, -558, 648, -554, 650, -558, 648, -558, 648, -554, 650, -556, 650, -1684, 624, -558, 648, -558, 648, -584, 620, -558, 648, -556, 650, -558, 648, -1684, 624, -558, 648, -1688, 616, -560, 648, -558, 646, -1686, 622, -558, 648]


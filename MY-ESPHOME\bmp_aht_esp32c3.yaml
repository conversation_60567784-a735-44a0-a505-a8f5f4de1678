esphome:
  name: bmp_aht_esp32c3
  friendly_name: 红外遥控, 温湿度大气压强传感器节点

esp32:
  board: airm2m_core_esp32c3
  framework:
    type: arduino
logger:
  level: DEBUG
  logs:
    remote_receiver: DEBUG
    remote_transmitter: DEBUG

api:

ota:
  platform: esphome

wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password

remote_transmitter:
  pin: GPIO19
  carrier_duty_percent: 50%
  id: my_transmitter

remote_receiver:
  pin: GPIO1
  dump: all
  id: my_receiver
#   tolerance: 50%
#   buffer_size: 10kb
#   filter: 250us
#   idle: 4ms

i2c:
  sda: 4
  scl: 5
  scan: true
  id: bus_a

sensor:
  # AHT20 Temperature and Humidity Sensor (Fixed address 0x38)
  - platform: aht10
    temperature:
      name: "AHT20 Temperature"
      id: room_temperature
    humidity:
      name: "AHT20 Humidity"
    update_interval: 60s

  # BMP280 Pressure Sensor
  - platform: bmp280_i2c
    temperature:
      name: "BMP280 Temperature"
    pressure:
      name: "BMP280 Pressure"
    address: 0x77
    i2c_id: bus_a
    update_interval: 60s

# climate:
#   - platform: gree
#     name: Gree Air Conditioner
#     model: yac1fb9
#     transmitter_id: my_transmitter
#     sensor: room_temperature
#     min_temperature: 16 °C
#     max_temperature: 30 °C

switch:
  # TECO协议空调电源开关
  - platform: template
    name: "TECO AC Power"
    id: teco_ac_power
    optimistic: true
    turn_on_action:
      - remote_transmitter.transmit_pronto:
          data: [
            0x0000, 0x006D, 0x0025, 0x0000, 0x0158, 0x00AE, 0x0015, 0x0045, 0x0013, 0x0019, 0x0015, 0x0019, 0x0013, 0x0045,
            0x0015, 0x001B, 0x0013, 0x001B, 0x0013, 0x0049, 0x000F, 0x0019, 0x0014, 0x001A, 0x0014, 0x0049, 0x0010, 0x0047,
            0x0012, 0x0048, 0x000F, 0x0022, 0x000B, 0x001C, 0x0014, 0x001B, 0x0012, 0x001A, 0x0015, 0x0019, 0x0015, 0x0019,
            0x0014, 0x001C, 0x0012, 0x0019, 0x001A, 0x0015, 0x0015, 0x004D, 0x000A, 0x001E, 0x0011, 0x0018, 0x0015, 0x001A,
            0x0014, 0x001D, 0x000B, 0x001F, 0x0016, 0x001C, 0x000F, 0x0047, 0x0015, 0x001C, 0x0011, 0x0043, 0x0014, 0x001B,
            0x0013, 0x0020, 0x000E, 0x0044, 0x0014, 0x001C, 0x0013, 0x0180
          ]
    turn_off_action:
      - remote_transmitter.transmit_raw:
          transmitter_id: my_transmitter
          carrier_frequency: 38kHz
          code: [8980, -4518, 606, -1682, 614, -576, 582, -606, 606, -584, 582, -606, 612, -576, 582, -1708, 580, -608, 582, -610, 606, -1682, 582, -608, 580, -1706, 582, -610, 578, -610, 580, -608, 580, -608, 582, -608, 582, -606, 610, -580, 608, -580, 580, -610, 580, -1708, 606, -582, 582, -608, 606, -582, 606, -584, 608, -580, 608, -582, 578, -1710, 582, -610, 578, -1710, 604, -584, 580, -608, 580, -1708, 608, -582, 582]
      - delay: 80ms
      - remote_transmitter.transmit_raw:
            transmitter_id: my_transmitter
            carrier_frequency: 38kHz
            code: [8990, -4530, 554, -1736, 578, -590, 598, -612, 578, -610, 576, -612, 576, -614, 576, -1712, 576, -612, 578, -610, 578, -1712, 578, -612, 578, -1710, 576, -612, 576, -612, 576, -612, 578, -612, 578, -612, 576, -612, 578, -612, 576, -612, 576, -614, 576, -1712, 576, -614, 576, -612, 576, -614, 576, -612, 576, -612, 576, -614, 578, -1712, 576, -1714, 576, -1714, 574, -614, 576, -614, 576, -1714, 576, -614, 576]

button:
  # TECO协议空调升温按钮
  - platform: template
    name: "TECO AC Temperature Up"
    on_press:
      - remote_transmitter.transmit_raw:
          transmitter_id: my_transmitter
          carrier_frequency: 38kHz
          code: [9022, -4484, 650, -1682, 624, -556, 650, -556, 652, -1682, 624, -1684, 622, -1684, 624, -554, 652, -558, 646, -1682, 624, -1682, 626, -582, 624, -1682, 626, -554, 652, -556, 650, -554, 652, -556, 648, -556, 650, -584, 624, -556, 648, -554, 652, -556, 650, -1682, 624, -558, 650, -554, 650, -556, 652, -556, 650, -582, 624, -556, 648, -1682, 624, -582, 624, -1682, 626, -554, 650, -556, 650, -1682, 624, -556, 650]

  # TECO协议空调降温按钮
  - platform: template
    name: "TECO AC Temperature Down"
    on_press:
      - remote_transmitter.transmit_raw:
          transmitter_id: my_transmitter
          carrier_frequency: 38kHz
          code: [8988, -4488, 658, -1680, 624, -558, 650, -554, 676, -1656, 626, -1682, 626, -1684, 624, -584, 624, -554, 650, -554, 652, -1682, 624, -558, 650, -1684, 624, -582, 622, -558, 648, -554, 650, -558, 648, -554, 650, -558, 648, -558, 648, -554, 650, -556, 650, -1684, 624, -558, 648, -558, 648, -584, 620, -558, 648, -556, 650, -558, 648, -1684, 624, -558, 648, -1688, 616, -560, 648, -558, 646, -1686, 622, -558, 648]

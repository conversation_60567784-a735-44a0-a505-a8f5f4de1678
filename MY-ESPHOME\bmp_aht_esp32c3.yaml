esphome:
  name: bmp_aht_esp32c3
  friendly_name: 红外遥控, 温湿度大气压强传感器节点

esp32:
  board: airm2m_core_esp32c3
  framework:
    type: arduino
logger:

api:

ota:
  platform: esphome

wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password

remote_transmitter:
  pin: GPIO19
  carrier_duty_percent: 50%
  id: my_transmitter

remote_receiver:
  pin: GPIO18
  dump: all
  id: my_receiver

i2c:
  sda: 4
  scl: 5
  scan: true
  id: bus_a

sensor:
  # AHT20 Temperature and Humidity Sensor (Fixed address 0x38)
  - platform: aht10
    temperature:
      name: "AHT20 Temperature"
      id: room_temperature
    humidity:
      name: "AHT20 Humidity"
    update_interval: 60s

  # BMP280 Pressure Sensor
  - platform: bmp280_i2c
    temperature:
      name: "BMP280 Temperature"
    pressure:
      name: "BMP280 Pressure"
    address: 0x77
    i2c_id: bus_a
    update_interval: 60s

climate:
  - platform: gree
    name: Gree Air Conditioner
    model: yac1fb9
    transmitter_id: my_transmitter
    sensor: room_temperature
    min_temperature: 16 °C
    max_temperature: 30 °C

button:
  # TECO协议空调开机按钮
  - platform: template
    name: "TECO AC Power On"
    on_press:
      - remote_transmitter.transmit_raw:
          transmitter_id: my_transmitter
          carrier_frequency: 38kHz
          code: [8968, -4510, 628, -1682, 654, -536, 670, -550, 654, -1652, 628, -1678, 630, -1678, 630, -1680, 652, -552, 626, -578, 628, -1680, 628, -578, 628, -1680, 654, -554, 626, -578, 628, -576, 626, -580, 628, -578, 626, -580, 626, -580, 624, -580, 652, -554, 624, -1684, 624, -580, 626, -578, 628, -580, 628, -578, 654, -552, 650, -556, 624, -1684, 624, -582, 624, -1680, 652, -554, 652, -554, 626, -1684, 624, -580, 626]

  # TECO协议空调关机按钮
  - platform: template
    name: "TECO AC Power Off"
    on_press:
      - remote_transmitter.transmit_raw:
          transmitter_id: my_transmitter
          carrier_frequency: 38kHz
          code: [8984, -4488, 650, -1684, 624, -556, 650, -554, 648, -554, 652, -1682, 624, -1684, 624, -1684, 624, -560, 646, -556, 650, -1682, 624, -554, 650, -1684, 624, -582, 624, -554, 652, -554, 650, -556, 650, -556, 650, -556, 650, -556, 648, -558, 648, -582, 624, -1684, 624, -554, 652, -554, 650, -556, 648, -558, 648, -554, 650, -558, 648, -1682, 624, -560, 646, -1684, 626, -552, 650, -556, 652, -1682, 624, -582, 622]

